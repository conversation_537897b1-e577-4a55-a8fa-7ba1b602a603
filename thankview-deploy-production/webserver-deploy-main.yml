- name: Deploy to web servers
  hosts: webserver_test_deployment #webservers
  vars:
    app_path: /var/www/thank-views
    git_repo: "**************:evertrue/ThankView-App.git"
    git_branch: "{{ deploy_branch | default('production') }}"

  tasks:
    - name: Ensure www-data home directory exists
      ansible.builtin.file:
        path: /home/<USER>
        state: directory
        mode: '0700'
        owner: www-data
        group: www-data
      become: true

    - name: Ensure .ssh directory exists for www-data
      ansible.builtin.file:
        path: /home/<USER>/.ssh
        state: directory
        mode: '0700'
        owner: www-data
        group: www-data
      become: true

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-prod/thankview_deploy" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: /home/<USER>/.ssh/thankview_deploy
        mode: '0600'
        owner: www-data
        group: www-data
      become: true
      no_log: true

    - name: Ensure correct permissions for SSH key
      ansible.builtin.file:
        path: /home/<USER>/.ssh/thankview_deploy
        state: file
        mode: '0600'
        owner: www-data
        group: www-data
      become: true

    - name: Change ownership of the application directory
      ansible.builtin.file:
        path: "{{ app_path }}"
        state: directory
        recurse: true
        owner: www-data
        group: www-data
        mode: '0775'
      become: true

    #This will reset repo and drop any local changes before pulling latest repo commit
    - name: Reset repository to the latest commit
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Allow Git to work in this repository
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory /var/www/thank-views
      become: true
      become_user: www-data

    - name: Pull latest code
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ git_branch }}"
        key_file: /home/<USER>/.ssh/thankview_deploy
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
        force: yes # Add this line to force the checkout not in stage playbook
      become: true
      become_user: www-data

    - name: Run composer update
      community.general.composer:
        command: update
        working_dir: "{{ app_path }}"
      become: true
      become_user: www-data
      register: composer_result
      failed_when:
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    - name: Ensure all dependencies are installed
      ansible.builtin.shell:
        cmd: composer install --optimize-autoloader --no-cache
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Save git commit hash
      ansible.builtin.shell: git log -1 --pretty=%h > git_commit
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    - name: Run composer dump-autoload
      community.general.composer:
        command: dump-autoload
        working_dir: "{{ app_path }}"
      become: true
      become_user: www-data
      register: composer_result
      failed_when:
        - composer_result.failed == true
        - "'ddtrace.so' not in composer_result.msg"
        - "'Script php artisan package:discover handling the post-autoload-dump event returned with error code 1' not in composer_result.msg"

    - name: Run Laravel migrations
      ansible.builtin.shell: php artisan migrate --force
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: www-data

    # - name: Run Laravel seeders
    #   ansible.builtin.shell: php artisan db:seed --force
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: www-data

    - name: Build frontend assets with npm
      ansible.builtin.shell:
        cmd: |
          npm i
          npm run dev
      args:
        chdir: "{{ app_path }}"
      environment:
        NODE_ENV: development
      become: true
      become_user: www-data

    # - name: Run custom bootstrap script
    #   ansible.builtin.shell: php artisan testData:bootstrap
    #   args:
    #     chdir: "{{ app_path }}"
    #   become: true
    #   become_user: www-data

    - name: Clear Laravel caches
      ansible.builtin.shell: "php artisan {{ item }}"
      args:
        chdir: "{{ app_path }}"
      loop:
        - "config:clear"
        - "route:clear"
        - "view:clear"
        - "static:clear"
      become: true
      become_user: www-data

    # New task to fetch and update .env files from AWS Secrets Manager
    - name: Overwrite .env files from Secrets Manager via lookup
      become: true
      block:
        - name: Fetch the full .env blob from Secrets Manager
          set_fact:
            env_json: "{{ lookup(
              'aws_secret',
              'prod/webserver-env',
              region='us-east-1'
              ) }}"

        - name: Convert JSON to .env format
          set_fact:
            env_content: |
              {% for key, value in env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Fetch the API-specific .env blob from Secrets Manager (using same secret for now)
          set_fact:
            api_env_json: "{{ lookup(
              'aws_secret',
              'prod/webserver-env',
              region='us-east-1'
              ) }}"

        - name: Convert API JSON to .env format
          set_fact:
            api_env_content: |
              {% for key, value in api_env_json.items() %}
              {{ key }}={{ value }}
              {% endfor %}

        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env

        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes

        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
            force: yes
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"

    # Task to set APP_ROLE based on server type
    - name: Set APP_ROLE based on server type
      become: true
      block:
        - name: Check if APP_ROLE already exists in .env file
          shell: grep -c "^APP_ROLE=" /var/www/thank-views/.env || true
          register: app_role_exists
          changed_when: false

        - name: Gather EC2 facts to get instance tags
          ec2_metadata_facts:
          register: ec2_facts

        - name: Get instance ID
          set_fact:
            instance_id: "{{ ec2_facts.ansible_facts.ansible_ec2_instance_id }}"
          when: ec2_facts is defined
          
        - name: Update apt cache accepting repository changes
          ansible.builtin.shell:
            cmd: apt update --allow-releaseinfo-change
          environment:
            DEBIAN_FRONTEND: noninteractive
          become: true
          ignore_errors: yes

        - name: Install Python pip and development tools
          apt:
            name:
              - python3-pip
              - python3-dev
              - python3-setuptools
            state: present
            update_cache: yes
          become: true

        - name: Install required Python modules for AWS operations
          pip:
            name:
              - boto3
              - botocore
            state: present
            executable: pip3
            extra_args: "--break-system-packages"
          become: true

        - name: Describe EC2 instance to get tags
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_facts.ansible_facts.ansible_ec2_placement_region }}"
          register: instance_info
          vars:
            ansible_python_interpreter: /usr/bin/python3
          when: instance_id is defined

        - name: Extract APP_ROLE from tags
          set_fact:
            app_role_value: "{{ item.value }}"
          loop: "{{ instance_info.instances[0].tags | dict2items }}"
          when:
            - instance_info is defined
            - instance_info.instances is defined
            - instance_info.instances | length > 0
            - item.key == 'APP_ROLE'

        - name: Debug APP_ROLE value from tags
          debug:
            msg: "Found APP_ROLE tag with value: {{ app_role_value }}"
          when: app_role_value is defined and app_role_value != ''

        - name: Set APP_ROLE in .env file from instance tag
          lineinfile:
            path: /var/www/thank-views/.env
            regexp: '^APP_ROLE='
            line: "APP_ROLE={{ app_role_value }}"
            state: present
          when:
            - app_role_exists.stdout == "0" or app_role_exists.stdout == "1"
            - app_role_value is defined
            - app_role_value != ''

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true