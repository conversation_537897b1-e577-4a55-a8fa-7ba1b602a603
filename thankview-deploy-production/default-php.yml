---
- name: Install PHP 8.1 on Ubuntu 20.04
  hosts: aws_ec2
  become: yes
  tasks:
    - name: Remove any existing PHP repositories
      shell: |
        rm -f /etc/apt/sources.list.d/*sury* /etc/apt/sources.list.d/*php* /etc/apt/sources.list.d/*ondrej*
        apt-key del 14AA40EC0831756756D7F66C4F4EA0AAE5267A6C 2>/dev/null || true
      ignore_errors: yes

    - name: Install prerequisites for PHP 8.1
      apt:
        name:
          - ca-certificates
          - apt-transport-https
          - software-properties-common
          - gnupg
          - lsb-release
        state: present
        update_cache: yes

    - name: Add Ondrej PHP PPA repository for focal
      apt_repository:
        repo: "deb http://ppa.launchpad.net/ondrej/php/ubuntu focal main"
        state: present
        update_cache: no

    - name: Add Ondrej PHP PPA repository for bionic (where PHP 8.1 is)
      apt_repository:
        repo: "deb http://ppa.launchpad.net/ondrej/php/ubuntu bionic main"
        state: present
        update_cache: no

    - name: Add Ondrej PHP PPA GPG key
      apt_key:
        keyserver: keyserver.ubuntu.com
        id: 14AA40EC0831756756D7F66C4F4EA0AAE5267A6C
        state: present

    - name: Update apt cache after adding repository
      apt:
        update_cache: yes
        cache_valid_time: 0

    - name: Check available PHP versions
      command: update-alternatives --list php
      register: php_versions
      changed_when: false
      failed_when: false

    - name: Debug available PHP versions
      debug:
        msg: "Available PHP versions: {{ php_versions.stdout_lines }}"

    - name: Install PHP 8.1 and core packages
      apt:
        name:
          - php8.1
          - php8.1-common
          - php8.1-cli
          - php8.1-fpm
        state: present
        update_cache: yes
      register: php_install

    - name: Set PHP 8.1 as default
      alternatives:
        name: php
        path: /usr/bin/php8.1
      register: php_alternative

    - name: Check current PHP version
      command: php -v
      register: php_version
      changed_when: false

    - name: Debug current PHP version
      debug:
        msg: "Current PHP version: {{ php_version.stdout_lines[0] }}"

    - name: Enable PHP 8.1-FPM service
      systemd:
        name: php8.1-fpm
        enabled: yes
        state: started
      register: fpm_enabled

    - name: Disable PHP 8.3-FPM service if it exists
      systemd:
        name: php8.3-fpm
        enabled: no
        state: stopped
      register: disable_php83
      failed_when: false
      when: php_version.stdout is regex('PHP 8.3')

    - name: Install common PHP 8.1 extensions
      apt:
        name:
          - php8.1-mysql
          - php8.1-curl
          - php8.1-mbstring
          - php8.1-xml
          - php8.1-zip
          - php8.1-bcmath
          - php8.1-intl
          - php8.1-gd
        state: present

    - name: Restart Apache2 web server
      systemd:
        name: apache2
        state: restarted
      failed_when: false
      when: php_alternative is changed or php_install is changed or fpm_enabled is changed